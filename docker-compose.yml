services:
  backend:
    build:
      context: ./backend
    container_name: gw-backend
    ports:
      - "5020:5020"
    env_file:
      - ./backend/.env
    volumes:
      - ./backend:/app
      - ./backend/node_modules:/app/node_modules
  
  # frontend:
  #   build:
  #     context: ./frontend
  #   container_name: gw-frontend
  #   ports:
  #     - '3010:3000'
  #   volumes:
  #     - ./frontend:/app
  #     - ./frontend/node_modules:/app/node_modules