services:

  db:
    image: postgres
    container_name: gw-db
    ports:
      - 5432:5432
    restart: always
    healthcheck:
      test: [ "CMD-SHELL", "sh -c 'pg_isready -U admin -d gw'" ]
      interval: 10s
      timeout: 3s
      retries: 3
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
      POSTGRES_DB: gw
    volumes:
      - pgdata:/var/lib/postgresql/data

  adminer:
    image: adminer
    restart: always
    ports:
      - 4000:8080

  backend:
    build:
      context: ./backend
    container_name: gw-backend
    ports:
      - "5020:5020"
    depends_on:
      db:
        condition: service_healthy
    env_file:
      - ./backend/.env
    volumes:
      - ./backend:/app
      - ./backend/node_modules:/app/node_modules
  
  # frontend:
  #   build:
  #     context: ./frontend
  #   container_name: gw-frontend
  #   ports:
  #     - '3010:3000'
  #   volumes:
  #     - ./frontend:/app
  #     - ./frontend/node_modules:/app/node_modules
volumes:
  pgdata:
    driver: local