"use client";

import { AppLayout } from '@/components/layout';

export default function TestLayoutPage() {
  const handleCreateAsset = () => {
    console.log('Create asset clicked');
  };

  return (
    <AppLayout
      sidebarProps={{
        campaignName: "My Campaign"
      }}
      topbarProps={{
        title: "Assets",
        showCreateButton: true,
        createButtonText: "Create asset",
        onCreateClick: handleCreateAsset,
        userName: "Armel"
      }}
    >
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold text-foreground">Filter by type:</h2>
          <div className="flex gap-2">
            <button className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded-md">
              All
            </button>
            <button className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md">
              Sales
            </button>
            <button className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md">
              Email
            </button>
            <button className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md">
              Video scripts
            </button>
            <button className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md">
              Lead magnet builder
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-card border border-border rounded-lg p-6">
            <h3 className="text-lg font-semibold text-card-foreground mb-2">Ninja nurture sequence</h3>
            <p className="text-sm text-muted-foreground mb-4">Email</p>
            <div className="w-full bg-secondary rounded-full h-2">
              <div className="bg-primary h-2 rounded-full" style={{ width: '50%' }}></div>
            </div>
            <p className="text-sm text-muted-foreground mt-2">50%</p>
          </div>
          
          <div className="bg-card border border-border rounded-lg p-6">
            <h3 className="text-lg font-semibold text-card-foreground mb-2">Lead magnets</h3>
            <p className="text-sm text-muted-foreground mb-4">Sales • Video scripts • Lead magnet builder</p>
            <div className="w-full bg-secondary rounded-full h-2">
              <div className="bg-primary h-2 rounded-full" style={{ width: '0%' }}></div>
            </div>
            <p className="text-sm text-muted-foreground mt-2">0%</p>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
