import Page from "@/components/page";
import Translation from "@/components/translation";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";

export default function AssetsPage(){
    const {
        register,
        handleSubmit
    } = useForm();
    const onSubmit = (values)=>{
        
    }
    return <Page
            title={<Translation block="Buttons">Generate by AI</Translation>}
        >
            <form action="" onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-12">
                <Textarea {...register("prompt")} />
                <Button type="submit" className="w-[25%]">
                    <Translation block="Buttons">Generate</Translation>
                </Button>
            </form>
    </Page>
}