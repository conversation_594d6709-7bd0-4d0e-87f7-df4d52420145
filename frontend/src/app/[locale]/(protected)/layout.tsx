import { Sidebar, Topbar } from "@/components/layout";
import { ReactNode } from "react";

export default function ProtectedLayout({children}: {children: ReactNode}){
    return <div>
        <div className="flex h-screen bg-background">
      <Sidebar campaignName="My Campaign" />
      
      <div className="flex-1 flex flex-col">
        <Topbar 
          title="Assets"
          showCreateButton={true}
          createButtonText="Create asset"
          userName="Armel"
        />
        
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
    </div>
}