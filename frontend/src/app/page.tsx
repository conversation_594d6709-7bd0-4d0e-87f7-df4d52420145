"use client"

import useCampaignPlanner from "@/components/hooks/api-calls/use-campaign-planner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { CampaignGoalsCard } from "@/components/campaign/campaign-goals-card";
import { TargetAvatarCard } from "@/components/campaign/target-avatar-card";
import { IdealClientCard } from "@/components/campaign/ideal-client-card";
import { CurrencyCalculatorCard } from "@/components/campaign/currency-calculator-card";

interface CampaignData {
  goals: {
    product_type: string;
    product_price: string;
    delivery_timeline: string;
    product_name: string[];
    general_topic: string;
  };
  targetAvatar: {
    avatars: string[];
  };
  idealClient: {
    accept_criteria: Array<{
      category: string;
      criteria: string;
    }>;
    reject_criteria: Array<{
      category: string;
      criteria: string;
    }>;
  };
  currencyCal: {
    increase_benefits: string[];
    decrease_benefits: string[];
  };
}

export default function Home() {
  const [campaign, setCampaign] = useState<CampaignData | null>(null);
  const {
    register,
    handleSubmit,
  } = useForm<{ prompt: string }>();
  const campaignMutation = useCampaignPlanner()
  const onSubmit = (data: { prompt: string }) => {
    console.log(data);
    campaignMutation.mutate({user_input: data.prompt}, {
      onSuccess: (data: CampaignData) => {
        setCampaign(data);
        console.log(data);
      }
    })
  };
  return (
    <div className="w-full p-16">
      <div className={`flex gap-8 ${campaign ? 'items-start' : 'items-center justify-center'}`}>
        {/* Form Section */}
        <div className={`${campaign ? 'w-1/3' : 'w-full max-w-md'}`}>
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
            <Textarea
              {...register("prompt")}
              placeholder="Enter your campaign prompt..."
              className="min-h-32"
            />
            <Button
              type="submit"
              variant={"outline"}
              disabled={campaignMutation.isPending}
            >
              {campaignMutation.isPending ? 'Generating...' : 'Submit'}
            </Button>
          </form>
        </div>

        {/* Cards Section - Only show if campaign data exists */}
        {campaign && (
          <div className="w-2/3">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <CampaignGoalsCard goals={campaign.goals} />
              <TargetAvatarCard targetAvatar={campaign.targetAvatar} />
              <IdealClientCard idealClient={campaign.idealClient} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
