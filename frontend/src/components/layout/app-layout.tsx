"use client";

import React from 'react';
import { Sidebar } from './sidebar';
import { Topbar } from './topbar';
import { cn } from '@/lib/utils';

interface AppLayoutProps {
  children: React.ReactNode;
  className?: string;
  sidebarProps?: {
    campaignName?: string;
  };
  topbarProps?: {
    title?: string;
    showCreateButton?: boolean;
    createButtonText?: string;
    onCreateClick?: () => void;
    userName?: string;
  };
}

export function AppLayout({ 
  children, 
  className,
  sidebarProps,
  topbarProps 
}: AppLayoutProps) {
  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <Sidebar {...sidebarProps} />
      
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Topbar */}
        <Topbar {...topbarProps} />
        
        {/* Page Content */}
        <main 
          className={cn(
            "flex-1 overflow-auto bg-background p-6",
            className
          )}
        >
          {children}
        </main>
      </div>
    </div>
  );
}
