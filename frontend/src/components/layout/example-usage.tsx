// Example usage of the layout components

import { AppLayout } from './app-layout';

export function ExamplePage() {
  const handleCreateAsset = () => {
    console.log('Create asset clicked');
  };

  return (
    <AppLayout
      sidebarProps={{
        campaignName: "My Campaign"
      }}
      topbarProps={{
        title: "Assets",
        showCreateButton: true,
        createButtonText: "Create asset",
        onCreateClick: handleCreateAsset,
        userName: "Armel"
      }}
    >
      {/* Your page content goes here */}
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold">Filter by type:</h2>
          <div className="flex gap-2">
            <button className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded-md">
              All
            </button>
            <button className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md">
              Sales
            </button>
            <button className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md">
              Email
            </button>
            <button className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md">
              Video scripts
            </button>
            <button className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md">
              Lead magnet builder
            </button>
          </div>
        </div>
        
        {/* Your content cards would go here */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Example cards */}
        </div>
      </div>
    </AppLayout>
  );
}

// Alternative: Using components separately
export function ExamplePageSeparate() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar campaignName="My Campaign" />
      
      <div className="flex-1 flex flex-col">
        <Topbar 
          title="Assets"
          showCreateButton={true}
          createButtonText="Create asset"
          userName="Armel"
        />
        
        <main className="flex-1 overflow-auto p-6">
          {/* Your content */}
        </main>
      </div>
    </div>
  );
}
