"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { 
  Home, 
  Target, 
  Map, 
  Palette, 
  Package,
  Plus
} from 'lucide-react';

interface SidebarItem {
  id: string;
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  isActive?: boolean;
}

interface SidebarProps {
  className?: string;
  campaignName?: string;
}

const sidebarItems: SidebarItem[] = [
  {
    id: 'home',
    label: 'Home',
    href: '/',
    icon: Home,
  },
  {
    id: 'campaign-planner',
    label: 'Campaign planner',
    href: '/campaign-planner',
    icon: Target,
  },
  {
    id: 'product-roadmap',
    label: 'Product roadmap',
    href: '/product-roadmap',
    icon: Map,
  },
  {
    id: 'design-studio',
    label: 'Design studio',
    href: '/design-studio',
    icon: Palette,
  },
  {
    id: 'assets',
    label: 'Assets',
    href: '/assets',
    icon: Package,
  },
];

export function Sidebar({ className, campaignName = "Campaign name" }: SidebarProps) {
  const pathname = usePathname();
  return (
    <aside 
      className={cn(
        "flex flex-col w-64 h-screen bg-sidebar border-r border-sidebar-border",
        className
      )}
    >
      {/* Company Header */}
      <div className="flex items-center gap-3 p-4 border-b border-sidebar-border">
        <div className="w-6 h-6 bg-sidebar-primary rounded-sm flex items-center justify-center">
          <div className="w-3 h-3 bg-sidebar-primary-foreground rounded-sm" />
        </div>
        <span className="font-semibold text-sidebar-foreground">Company</span>
      </div>

      {/* Campaign Name Section */}
      <div className="p-4 border-b border-sidebar-border">
        <div className="flex items-center justify-between">
          <span className="text-sm text-sidebar-foreground font-medium">
            {campaignName}
          </span>
          <button className="text-sidebar-foreground hover:text-sidebar-primary transition-colors">
            <svg 
              width="12" 
              height="12" 
              viewBox="0 0 12 12" 
              fill="none" 
              className="rotate-180"
            >
              <path 
                d="M3 4.5L6 7.5L9 4.5" 
                stroke="currentColor" 
                strokeWidth="1.5" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 p-2">
        <ul className="space-y-1">
          {sidebarItems.map((item) => {
            const isActive = pathname === item.href || 
              (item.href !== '/' && pathname.startsWith(item.href));
            
            return (
              <li key={item.id}>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200",
                    "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                    isActive 
                      ? "bg-primary text-sidebar-primary-foreground shadow-sm" 
                      : "text-sidebar-foreground"
                  )}
                >
                  <item.icon className="w-4 h-4 flex-shrink-0" />
                  <span>{item.label}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Create Campaign Button */}
      <div className="p-4 border-t border-sidebar-border">
        <button className="flex items-center gap-2 text-sm text-sidebar-foreground hover:text-sidebar-primary transition-colors">
          <Plus className="w-4 h-4" />
          <span>Create campaign</span>
        </button>
      </div>
    </aside>
  );
}
