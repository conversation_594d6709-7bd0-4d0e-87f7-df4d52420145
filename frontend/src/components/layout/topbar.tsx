"use client";

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { 
  Search, 
  Bell, 
  ChevronDown,
  Plus
} from 'lucide-react';

interface TopbarProps {
  className?: string;
  title?: string;
  showCreateButton?: boolean;
  createButtonText?: string;
  onCreateClick?: () => void;
  userName?: string;
}

interface TabItem {
  id: string;
  label: string;
  href?: string;
  isActive?: boolean;
}

const tabs: TabItem[] = [
  {
    id: 'campaigns',
    label: 'Campaigns',
    isActive: true,
  },
  {
    id: 'websites',
    label: 'Websites',
    isActive: false,
  },
];

export function Topbar({ 
  className,
  title = "Assets",
  showCreateButton = true,
  createButtonText = "Create asset",
  onCreateClick,
  userName = "Armel"
}: TopbarProps) {
  const [activeTab, setActiveTab] = useState('campaigns');

  return (
    <header 
      className={cn(
        "flex items-center justify-between h-16 px-6 bg-background border-b border-border",
        className
      )}
    >
      {/* Left Section - Tabs and Title */}
      <div className="flex items-center gap-8">
        {/* Navigation Tabs */}
        <nav className="flex items-center">
          <ul className="flex items-center gap-6">
            {tabs.map((tab) => (
              <li key={tab.id}>
                <button
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "relative px-1 py-2 text-sm font-medium transition-colors",
                    "hover:text-foreground",
                    activeTab === tab.id
                      ? "text-foreground"
                      : "text-muted-foreground"
                  )}
                >
                  {tab.label}
                  {activeTab === tab.id && (
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary rounded-full" />
                  )}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Page Title */}
        <h1 className="text-2xl font-semibold text-foreground">
          {title}
        </h1>
      </div>

      {/* Right Section - Search, Notifications, User */}
      <div className="flex items-center gap-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search..."
            className={cn(
              "pl-10 pr-4 py-2 w-64 text-sm",
              "bg-background border border-input rounded-md",
              "placeholder:text-muted-foreground",
              "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
              "transition-all duration-200"
            )}
          />
        </div>

        {/* Notifications */}
        <button className="relative p-2 text-muted-foreground hover:text-foreground transition-colors">
          <Bell className="w-5 h-5" />
          <span className="absolute top-1 right-1 w-2 h-2 bg-destructive rounded-full" />
        </button>

        {/* User Menu */}
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-foreground hover:bg-accent hover:text-accent-foreground rounded-md transition-colors">
            <span>{userName}</span>
            <ChevronDown className="w-4 h-4" />
          </button>
        </div>

        {/* Create Button */}
        {showCreateButton && (
          <button
            onClick={onCreateClick}
            className={cn(
              "flex items-center gap-2 px-4 py-2 text-sm font-medium",
              "bg-primary text-primary-foreground",
              "hover:bg-primary/90",
              "rounded-md transition-colors",
              "shadow-sm"
            )}
          >
            <Plus className="w-4 h-4" />
            <span>{createButtonText}</span>
          </button>
        )}
      </div>
    </header>
  );
}
