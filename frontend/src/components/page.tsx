"use client"
import { PropsWithChildren, ReactElement, ReactNode } from "react";

interface IPageProps {
    title: string | ReactElement; 
    subTitle?: string;
    buttons?: ReactNode[];
}

export default function Page({title, subTitle, buttons, children}: PropsWithChildren<IPageProps>){
    return (
        <div className={`flex flex-col`}>
            <div className="flex flex-col">
                <div className="flex flex-col gap-6">
                    {typeof title !== "string" ? title : <h3>{title}</h3>}
                    <p>{subTitle}</p>
                </div>
                <div className="flex items-center justify-end flex-wrap">
                    {
                        buttons?.map((btn, index) => <div key={index}>{btn}</div>)
                    }
                </div>
            </div>
            {children}
        </div>
    )
}