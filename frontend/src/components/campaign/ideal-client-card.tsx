import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";

interface IdealClientProps {
  idealClient: {
    accept_criteria: Array<{
      category: string;
      criteria: string;
    }>;
    reject_criteria: Array<{
      category: string;
      criteria: string;
    }>;
  };
}

export function IdealClientCard({ idealClient }: IdealClientProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Ideal Client Criteria</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h4 className="font-semibold text-sm text-green-600 mb-3">Accept Criteria</h4>
          <div className="space-y-3">
            {idealClient.accept_criteria.map((item, index) => (
              <div key={index} className="border-l-2 border-green-200 pl-3">
                <h5 className="font-medium text-sm text-muted-foreground">{item.category}</h5>
                <p className="text-sm mt-1">{item.criteria}</p>
              </div>
            ))}
          </div>
        </div>
        
        <div>
          <h4 className="font-semibold text-sm text-red-600 mb-3">Reject Criteria</h4>
          <div className="space-y-3">
            {idealClient.reject_criteria.map((item, index) => (
              <div key={index} className="border-l-2 border-red-200 pl-3">
                <h5 className="font-medium text-sm text-muted-foreground">{item.category}</h5>
                <p className="text-sm mt-1">{item.criteria}</p>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
