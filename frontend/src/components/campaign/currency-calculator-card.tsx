import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

interface CurrencyCalculatorProps {
  currencyCal: {
    increase_benefits: string[];
    decrease_benefits: string[];
  };
}

export function CurrencyCalculatorCard({ currencyCal }: CurrencyCalculatorProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Currency Calculator</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h4 className="font-semibold text-sm text-green-600 mb-3">Increase Benefits</h4>
          <ul className="space-y-2">
            {currencyCal.increase_benefits.map((benefit, index) => (
              <li key={index} className="text-sm flex items-start">
                <span className="text-green-500 mr-2">+</span>
                {benefit}
              </li>
            ))}
          </ul>
        </div>
        
        <div>
          <h4 className="font-semibold text-sm text-red-600 mb-3">Decrease Benefits</h4>
          <ul className="space-y-2">
            {currencyCal.decrease_benefits.map((benefit, index) => (
              <li key={index} className="text-sm flex items-start">
                <span className="text-red-500 mr-2">-</span>
                {benefit}
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
