import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

interface CampaignGoalsProps {
  goals: {
    product_type: string;
    product_price: string;
    delivery_timeline: string;
    product_name: string[];
    general_topic: string;
  };
}

export function CampaignGoalsCard({ goals }: CampaignGoalsProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Campaign Goals</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-semibold text-sm text-muted-foreground mb-1">Product Type</h4>
          <p className="text-sm">{goals.product_type}</p>
        </div>
        
        <div>
          <h4 className="font-semibold text-sm text-muted-foreground mb-1">Product Price</h4>
          <p className="text-sm">{goals.product_price}</p>
        </div>
        
        <div>
          <h4 className="font-semibold text-sm text-muted-foreground mb-1">Delivery Timeline</h4>
          <p className="text-sm">{goals.delivery_timeline}</p>
        </div>
        
        <div>
          <h4 className="font-semibold text-sm text-muted-foreground mb-1">General Topic</h4>
          <p className="text-sm">{goals.general_topic}</p>
        </div>
        
        <div>
          <h4 className="font-semibold text-sm text-muted-foreground mb-2">Product Name Options</h4>
          <ul className="space-y-1">
            {goals.product_name.map((name, index) => (
              <li key={index} className="text-sm flex items-start">
                <span className="text-muted-foreground mr-2">•</span>
                {name}
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
