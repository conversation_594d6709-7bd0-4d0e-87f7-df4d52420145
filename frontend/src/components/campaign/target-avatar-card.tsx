import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface TargetAvatarProps {
  targetAvatar: {
    avatars: string[];
  };
}

export function TargetAvatarCard({ targetAvatar }: TargetAvatarProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Target Avatars</CardTitle>
      </CardHeader>
      <CardContent>
        <div>
          <h4 className="font-semibold text-sm text-muted-foreground mb-2">Target Audience</h4>
          <ul className="space-y-2">
            {targetAvatar.avatars.map((avatar, index) => (
              <li key={index} className="text-sm flex items-start">
                <span className="text-muted-foreground mr-2">•</span>
                {avatar}
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
