import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import Routes from './modules';

dotenv.config();

const app = express();
app.use(cors());
app.use(express.json());

app.get('/', (_, res) => res.send('API is running'));

const PORT = process.env.PORT || 5020;
app.listen(PORT, () => console.log(`Server is running on port ${PORT}`));

export default app;

app.use('/v1', Routes());