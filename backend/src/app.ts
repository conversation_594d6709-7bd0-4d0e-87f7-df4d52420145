import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import Routes from './modules';
import sequelize from './loaders/datasource';

dotenv.config();

const app = express();
app.use(cors());
app.use(express.json());

app.get('/', (_, res) => res.send('API is running'));

const PORT = process.env.PORT || 5020;
app.listen(PORT, () => console.log(`Server is running on port ${PORT}`));

app.use('/v1', Routes());
(async ()=>{
    try {
        await sequelize.sync({alter: true});
        console.log('Database Connection has been established successfully.');
      } catch (error) {
        console.error('Unable to connect to the database:', error);
      }
})();
export default app;
