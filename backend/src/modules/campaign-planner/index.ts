import axios from "axios";
import { Router, Request, Response } from "express";

const CampaignPlannerRouter = Router();

CampaignPlannerRouter.route('/campaign-planner').post([], async (req: Request, res: Response) => {
  try{
    const {data: goalsResult} = await axios.post('http://localhost:5678/webhook/generate-campaign-goals',req.body,{
  headers: {
    'Content-Type': 'application/json'
  }
});
    const goals = goalsResult.data.generated_content;
    const {data: targetAvatarResult} = await axios.post('http://localhost:5678/webhook/generate-target-avatar', {
         "campaign_goals": goals,
  "industry": goals.industry,
  "product_type": goals.product_type,
  "price_point": goals.product_price
    }, {
  headers: {
    'Content-Type': 'application/json'
  }
});
    const targetAvatar = targetAvatarResult.data.generated_content;
    const {data: idealClientRes} = await axios.post('http://localhost:5678/webhook/generate-ideal-customer', {
  "target_avatars": targetAvatar.avatars,
  "product_type": goals.product_type,
  "price_point": goals.product_price,
  "delivery_method": goals.product_type
}, {
  headers: {
    'Content-Type': 'application/json'
  }
});;
const idealClient = idealClientRes.data.generated_content;
// const {data: currencyCalRes} = await axios.post('http://localhost:5678/webhook/generate-currency-calculator', {
//   "product_description": goals.general_topic,
//   "target_avatars": targetAvatar.avatars,
//   "industry": goals.industry,
// }, {
//   headers: {
//     'Content-Type': 'application/json'
//   }
// });;

// const currencyCal = currencyCalRes.data.generated_content;


    
    res.send({goals, targetAvatar, idealClient});
  }catch(err){
    console.log(err);
    res.status(500).send(err);
  }
});

export default CampaignPlannerRouter;
