import { Sequelize, DataTypes, Model, InferAttributes, InferCreationAttributes, CreationOptional } from 'sequelize';
import sequelize from '@/loaders/datasource';
import { Project } from '../projects/model';

export class Asset extends Model<InferAttributes<Asset>, InferCreationAttributes<Asset>> {
    declare id: CreationOptional<string>;
    declare project_id: CreationOptional<string>;
    declare name: string;
    declare type: string;
    declare content: string;
    declare metadata: CreationOptional<JSON>;
    declare description: CreationOptional<string>;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;
}

Asset.init(
  {
    id: {
        type: DataTypes.UUID,
        primaryKey: true,
        autoIncrement: false,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
    },
    project_id: {
        type: DataTypes.UUID,
        allowNull: false,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    content: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    // Other model options go here
    sequelize, // We need to pass the connection instance
    modelName: 'Asset', // We need to choose the model name
  },
);

Project.hasMany(Asset, {
    as: 'assets',
    foreignKey: {
        name: 'project_id',
    }
});

Asset.belongsTo(Project, {
    foreignKey: {
        name: 'project_id',
    }
})