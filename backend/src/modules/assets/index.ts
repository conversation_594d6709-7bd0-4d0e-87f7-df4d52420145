import { Router, Request, Response } from "express";
import { Project } from "../projects/model";
import { Asset } from "./model";
import axios from "axios";

export const AssetsRouter = Router();

AssetsRouter.route('/')
.get([], async(req:Request, res: Response)=>{
    const assets = await Asset.findAll(req.query);
    res.status(200).send(assets);
})
.post([], async (req:Request, res: Response)=>{
    try{
        const {data} = await axios.post('', {
            ...req.body,
            lang: 'French'
        });
        const mainData = data?.data?.generated_content;

const { slides_html, email_sequence, email_templates } = mainData;

// Store the slides
await Asset.create({
    type: 'html_slide',
    name: 'Main Slide Deck',
    content: slides_html
  });

// Store the email sequence metadata
  const email_seq_data = email_sequence.map((item, idx) =>
    ({
        type: 'email_sequence',
        name: `Email ${idx + 1}`,
        content: `${item.subject}: ${item.description}`,
      })
  );

  await Asset.bulkCreate(email_seq_data);

// Store the full email templates
const email_template_data = email_templates.map((item, idx) =>
    ({
        type: 'email_template',
        name: item.subject,
        content: item.body
      })
  )

  await Asset.bulkCreate(email_template_data);
  res.status(200).send({success: true});

    }catch(err){
        console.log(err);
        res.send(err);
    }
})