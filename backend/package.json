{"name": "backend", "version": "1.0.0", "main": "server.js", "license": "MIT", "scripts": {"dev": "nodemon src/server.ts", "start": "node dist/server.js", "build": "tsc"}, "dependencies": {"axios": "^1.11.0", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "zod": "^4.0.10"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.1.0", "nodemon": "^3.1.10", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7"}}