"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = isLowercase;
var _assertString = _interopRequireDefault(require("./util/assertString"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
function isLowercase(str) {
  (0, _assertString.default)(str);
  return str === str.toLowerCase();
}
module.exports = exports.default;
module.exports.default = exports.default;